/**
 * Application-wide constants and default values
 * Centralizes default settings for consistent usage across components
 */

import { ReactElement } from 'react'

// Default language setting (Catalan as primary language)
export const DEFAULT_LANGUAGE = 'ca' as const

// Default table pagination settings
export const DEFAULT_ROWS_PER_PAGE = 10
export const ROWS_PER_PAGE_OPTIONS = [10, 20, 30, 40, 50] as const

// Table pagination size constants
export const DEFAULT_PAGE_SIZE = 10
export const DEFAULT_PAGE_NUMBER = 1

// Common loading states
export const DEFAULT_LOADING_TEXT = 'loading' // Translation key
export const DEFAULT_ERROR_TEXT = 'error' // Translation key

// Supported locales
export const SUPPORTED_LOCALES = ['ca', 'es'] as const
export type SupportedLocale = typeof SUPPORTED_LOCALES[number]

// Form validation defaults
export const DEFAULT_FORM_MESSAGES = {
  REQUIRED_FIELD: 'validation.required',
  INVALID_EMAIL: 'validation.invalidEmail',
  INVALID_FORMAT: 'validation.invalidFormat',
} as const

// Toast message types for translation keys
export const TOAST_MESSAGES = {
  SUCCESS: {
    CREATE: 'success.created',
    UPDATE: 'success.updated', 
    DELETE: 'success.deleted',
  },
  ERROR: {
    CREATE: 'error.create',
    UPDATE: 'error.update',
    DELETE: 'error.delete',
    GENERIC: 'error.generic',
    REQUIRED_FIELD: 'error.requiredField',
  }
} as const

// Common UI component defaults
export const UI_DEFAULTS = {
  SPINNER_SIZE: 32,
  SPINNER_VARIANT: 'infinite' as const,
  MODAL_MAX_WIDTH: 'lg' as const,
  BUTTON_LOADING_TEXT: 'common.loading',
} as const

// Header/Footer specific constants
export const HEADER_FOOTER_TYPES = ['header', 'footer'] as const
export type HeaderFooterType = typeof HEADER_FOOTER_TYPES[number]

// Search and filter defaults
export const SEARCH_DEBOUNCE_MS = 300
export const DEFAULT_SEARCH_PLACEHOLDER = 'common.search'

export default {
  DEFAULT_LANGUAGE,
  DEFAULT_ROWS_PER_PAGE,
  ROWS_PER_PAGE_OPTIONS,
  DEFAULT_PAGE_SIZE,
  DEFAULT_PAGE_NUMBER,
  SUPPORTED_LOCALES,
  DEFAULT_FORM_MESSAGES,
  TOAST_MESSAGES,
  UI_DEFAULTS,
  HEADER_FOOTER_TYPES,
  SEARCH_DEBOUNCE_MS,
  DEFAULT_SEARCH_PLACEHOLDER,
}
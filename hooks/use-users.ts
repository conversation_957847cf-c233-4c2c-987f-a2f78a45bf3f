import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { User, CreateUserRequest, UpdateUserRequest } from '@/types/user'
import { useAuthFetch } from './use-auth-fetch'

interface UseUsersReturn {
  users: User[]
  loading: boolean
  error: string | null
  refetch: () => void
  createUser: (user: CreateUserRequest) => Promise<User>
  updateUser: (id: string, user: UpdateUserRequest) => Promise<User>
  deleteUser: (id: string) => Promise<void>
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://127.0.0.1:8000'

export function useUsers(): UseUsersReturn {
  const { data: session } = useSession()
  const { authFetch } = useAuthFetch()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchUsers = useCallback(async () => {
    if (!session?.djangoAccessToken) {
      setError('Authentication required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await authFetch(`${API_BASE_URL}/users/users/`, {
        method: 'GET',
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.statusText}`)
      }

      const data: User[] = await response.json()
      setUsers(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setUsers([])
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, authFetch])

  const createUser = useCallback(async (user: CreateUserRequest): Promise<User> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    const response = await authFetch(`${API_BASE_URL}/users/create_temp/`, {
      method: 'POST',
      body: JSON.stringify(user),
    })

    if (!response.ok) {
      throw new Error(`Failed to create user: ${response.statusText}`)
    }

    const newUser: User = await response.json()
    setUsers(prev => [...prev, newUser])
    return newUser
  }, [session?.djangoAccessToken, authFetch])

  const updateUser = useCallback(async (id: string, user: UpdateUserRequest): Promise<User> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    const response = await authFetch(`${API_BASE_URL}/users/update-user-group/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(user),
    })

    if (!response.ok) {
      throw new Error(`Failed to update user: ${response.statusText}`)
    }

    const updatedUser: User = await response.json()
    setUsers(prev => prev.map(u => u.id === id ? updatedUser : u))
    return updatedUser
  }, [session?.djangoAccessToken, authFetch])

  const deleteUser = useCallback(async (id: string): Promise<void> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    const response = await authFetch(`${API_BASE_URL}/users/delete/${id}/`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      throw new Error(`Failed to delete user: ${response.statusText}`)
    }

    setUsers(prev => prev.filter(u => u.id !== id))
  }, [session?.djangoAccessToken, authFetch])

  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  return {
    users,
    loading,
    error,
    refetch: fetchUsers,
    createUser,
    updateUser,
    deleteUser
  }
}

import { useCallback } from 'react'
import { useSession } from 'next-auth/react'
import useUserS<PERSON> from '@/store/use-user-store'

interface AuthFetchOptions extends RequestInit {
  skipAuth?: boolean
}

interface AuthFetchReturn {
  authFetch: (url: string, options?: AuthFetchOptions) => Promise<Response>
  getAuthHeaders: () => Record<string, string>
}

/**
 * Custom hook that provides authenticated fetch with automatic token refresh
 */
export function useAuthFetch(): AuthFetchReturn {
  const { data: session } = useSession()
  const { user, refreshToken } = useUserStore()

  const getAuthHeaders = useCallback(() => {
    const token = user?.session?.accessToken || session?.djangoAccessToken
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  }, [user?.session?.accessToken, session?.djangoAccessToken])

  const authFetch = useCallback(async (url: string, options: AuthFetchOptions = {}): Promise<Response> => {
    const { skipAuth, ...fetchOptions } = options
    
    // Get current token
    const token = user?.session?.accessToken || session?.djangoAccessToken
    
    if (!token && !skipAuth) {
      throw new Error('No authentication token available')
    }

    // Prepare headers
    const headers = {
      ...getAuthHeaders(),
      ...fetchOptions.headers,
    }

    // Make the initial request
    let response = await fetch(url, {
      ...fetchOptions,
      headers: skipAuth ? fetchOptions.headers : headers,
    })

    // If unauthorized and we have a refresh token, try to refresh and retry
    if (response.status === 401 && !skipAuth && user?.session?.refreshToken) {
      console.log('🔄 Request failed with 401, attempting token refresh...')
      
      const refreshSuccess = await refreshToken(user.session.refreshToken)
      
      if (refreshSuccess) {
        console.log('✅ Token refreshed, retrying request...')
        
        // Get the updated token from store
        const updatedUser = useUserStore.getState().user
        const newToken = updatedUser?.session?.accessToken
        
        if (newToken) {
          // Retry the request with the new token
          const updatedHeaders = {
            ...headers,
            'Authorization': `Bearer ${newToken}`
          }
          
          response = await fetch(url, {
            ...fetchOptions,
            headers: updatedHeaders,
          })
        }
      } else {
        console.log('❌ Token refresh failed, request will fail with 401')
      }
    }

    return response
  }, [user, session, getAuthHeaders, refreshToken])

  return {
    authFetch,
    getAuthHeaders
  }
}

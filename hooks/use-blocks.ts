import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Block, BlockResponse } from '@/types/block'

interface UseBlocksParams {
  search?: string
  brand?: string
  page?: number
  pageSize?: number
}

interface UseBlocksReturn {
  blocks: Block[]
  loading: boolean
  error: string | null
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  refetch: () => void
}

export function useBlocks(params: UseBlocksParams = {}): UseBlocksReturn {
  const { data: session } = useSession()
  const [blocks, setBlocks] = useState<Block[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [hasNext, setHasNext] = useState(false)
  const [hasPrevious, setHasPrevious] = useState(false)

  const fetchBlocks = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if user is authenticated
      if (!session) {
        setError('User not authenticated')
        setBlocks([])
        setTotalCount(0)
        setHasNext(false)
        setHasPrevious(false)
        return
      }

      if (!session.djangoAccessToken) {
        setError('Django access token not available. Please try logging in again.')
        setBlocks([])
        setTotalCount(0)
        setHasNext(false)
        setHasPrevious(false)
        return
      }

      const queryParams = new URLSearchParams()
      if (params.search) queryParams.append('search', params.search)
      if (params.brand) queryParams.append('brand', params.brand)
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://10.10.10.10/apiv1'

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/blocks/list-blocks/?${queryParams.toString()}`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch blocks: ${response.statusText}`)
      }

      const data: BlockResponse = await response.json()

      setBlocks(data.results)
      setTotalCount(data.count)
      setHasNext(!!data.next)
      setHasPrevious(!!data.previous)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setBlocks([])
      setTotalCount(0)
      setHasNext(false)
      setHasPrevious(false)
    } finally {
      setLoading(false)
    }
  }, [params.search, params.brand, params.page, params.pageSize, session])

  useEffect(() => {
    fetchBlocks()
  }, [fetchBlocks])

  return {
    blocks,
    loading,
    error,
    totalCount,
    hasNext,
    hasPrevious,
    refetch: fetchBlocks
  }
}

import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { HeaderFooter } from '@/types/block'

export interface CreateHeaderFooterRequest {
  name: string
  brand: string
  element_type: 'header' | 'footer'
  language: string
  html_content: string
  variables: Record<string, string>
  is_active: boolean
}

interface UseCreateHeaderFooterReturn {
  createHeaderFooter: (data: CreateHeaderFooterRequest) => Promise<HeaderFooter>
  loading: boolean
  error: string | null
}

export function useCreateHeaderFooter(): UseCreateHeaderFooterReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createHeaderFooter = useCallback(async (data: CreateHeaderFooterRequest): Promise<HeaderFooter> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    try {
      setLoading(true)
      setError(null)

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://172.16.249.87/apiv1'

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/blocks/create-header-footer/`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Failed to create header/footer: ${response.statusText}`)
      }

      const headerFooter: HeaderFooter = await response.json()
      return headerFooter
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken])

  return {
    createHeaderFooter,
    loading,
    error
  }
}

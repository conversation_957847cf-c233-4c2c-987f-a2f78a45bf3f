import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'

export interface UpdateTemplateData {
  id: string
  name: string
  brand: string
  description: string
  is_active: boolean
  template_blocks: { block_id: string; order_position: number }[]
}

export function useUpdateTemplate() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateTemplate = async (data: UpdateTemplateData) => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida. Torna a iniciar sessió.')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://***********/apiv1'

      const response = await fetch(`${backendUrl}/templates/update-template/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.djangoAccessToken}`
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `No s'ha pogut actualitzar la plantilla: ${response.statusText}`)
      }

      toast.success('Plantilla actualitzada correctament!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'No s\'ha pogut actualitzar la plantilla'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { updateTemplate, loading, error }
}


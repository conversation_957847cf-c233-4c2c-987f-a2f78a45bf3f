import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'

interface UpdateBlockData {
  id: string
  name: string
  brand: string
  html_content: string
  description: string
  variables: Record<string, string>
  is_editable: boolean
  is_active: boolean
}

interface UseUpdateBlockReturn {
  updateBlock: (data: UpdateBlockData) => Promise<void>
  loading: boolean
  error: string | null
}

export function useUpdateBlock(): UseUpdateBlockReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateBlock = async (data: UpdateBlockData) => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required. Please log in again.')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://***********/apiv1'
      
      const response = await fetch(`${backendUrl}/blocks/update-block/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.djangoAccessToken}`
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Failed to update block: ${response.statusText}`)
      }

      toast.success('Block updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update block'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    updateBlock,
    loading,
    error
  }
}

"use client"

import React, { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { NextPage } from 'next'
import { Plus, Search } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { TemplatesTable } from "@/components/table/templates-table"
import { useTemplates } from "@/hooks/use-templates"
import { useTranslations } from "next-intl"

interface Props {}

const Page: NextPage<Props> = ({}) => {
  const router = useRouter()
  const { status } = useSession()
  const t = useTranslations()

  const [searchQuery, setSearchQuery] = useState("")
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // Debounce search
  const [debouncedSearch, setDebouncedSearch] = useState("")
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedSearch(searchQuery), 400)
    return () => clearTimeout(handler)
  }, [searchQuery])

  const { templates, loading, error, refetch, totalCount, hasNext, hasPrevious } = useTemplates({
    search: debouncedSearch || undefined,
    page,
    pageSize,
  })

  const handleCreate = () => {
    router.push('/templates/create')
  }

  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">{t('common.loading')}</div>
        </div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
    <div className="p-6 space-y-8">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('templates.title')}</h1>
            <p className="text-muted-foreground">{t('templates.description')}</p>
          </div>
          <Button onClick={handleCreate}>
            <Plus className="mr-2 h-4 w-4" />
            {t('templates.createTemplate')}
          </Button>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder={t('templates.searchPlaceholder')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {error && (
          <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {t('common.error')}: {error}
          </div>
        )}

        <TemplatesTable
          data={templates}
          loading={loading}
          onRefresh={refetch}
          pagination={{
            page,
            pageSize,
            totalCount,
            hasNext,
            hasPrevious,
            onPageChange: setPage,
            onPageSizeChange: setPageSize,
          }}
        />
      </div>
    </div>
  )
}

export default Page
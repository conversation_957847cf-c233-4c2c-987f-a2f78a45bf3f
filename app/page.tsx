"use client";

import React, { useState, useEffect } from "react";
import { useSession, signOut } from "next-auth/react";
import { useTranslations } from 'next-intl';

import { NewslettersTable } from "@/components/table/newsletters-table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { useNewsletters } from "@/hooks/use-newsletters";


export default function Home() {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const t = useTranslations();

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch newsletters with search
  const { newsletters, loading, error, totalCount } = useNewsletters({
    search: debouncedSearch || undefined,
    page: 1,
    pageSize: 50
  });

  return (
    <div className="p-6 space-y-6">
      <header className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">
            {t('newsletters.title')}
          </h1>
          {session && (
            <p className="text-sm text-muted-foreground">
              {t('common.welcome')}, {session.user?.name || session.user?.email}
            </p>
          )}
          {totalCount > 0 && (
            <p className="text-sm text-muted-foreground">
              {t('newsletters.newslettersFound', { 
                count: totalCount, 
                plural: totalCount !== 1 ? 's' : '' 
              })}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          {session && (
            <Button
              variant="outline"
              onClick={() => signOut({ callbackUrl: '/login' })}
            >
              {t('common.signOut')}
            </Button>
          )}
        </div>
      </header>
      <div>
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder={t('newsletters.searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {error && (
          <div className="mb-4 p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {t('common.error')}: {error}
          </div>
        )}

        <NewslettersTable data={newsletters} loading={loading} />
      </div>
    </div>
  );
}

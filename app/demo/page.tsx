"use client";

import { useTranslations } from 'next-intl';
import { LanguageSwitcher } from "@/components/ui/language-switcher";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export default function TranslationDemoPage() {
  const t = useTranslations();

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header with Language Switcher */}
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Translation Demo</h1>
          <LanguageSwitcher />
        </div>

        {/* Configuration Section */}
        <Card>
          <CardHeader>
            <CardTitle>{t('configuration.title')}</CardTitle>
            <CardDescription>{t('configuration.description')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">{t('configuration.userManagement.title')}</CardTitle>
                  <CardDescription>{t('configuration.userManagement.description')}</CardDescription>
                </CardHeader>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">{t('configuration.roleManagement.title')}</CardTitle>
                  <CardDescription>{t('configuration.roleManagement.description')}</CardDescription>
                </CardHeader>
              </Card>
            </div>
          </CardContent>
        </Card>

        {/* Common Terms */}
        <Card>
          <CardHeader>
            <CardTitle>Common Terms</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Badge variant="outline">{t('common.name')}</Badge>
                <Badge variant="outline">{t('common.email')}</Badge>
                <Badge variant="outline">{t('common.role')}</Badge>
              </div>
              <div className="space-y-2">
                <Badge variant="outline">{t('common.status')}</Badge>
                <Badge variant="outline">{t('common.actions')}</Badge>
                <Badge variant="outline">{t('common.loading')}</Badge>
              </div>
              <div className="space-y-2">
                <Button variant="outline" size="sm">{t('common.edit')}</Button>
                <Button variant="outline" size="sm">{t('common.delete')}</Button>
                <Button variant="outline" size="sm">{t('common.save')}</Button>
              </div>
              <div className="space-y-2">
                <Button variant="outline" size="sm">{t('common.cancel')}</Button>
                <Button variant="outline" size="sm">{t('common.create')}</Button>
                <Button variant="outline" size="sm">{t('common.close')}</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Management Terms */}
        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p><strong>{t('users.editUser')}:</strong> {t('users.editUserDescription')}</p>
              <p><strong>{t('users.deleteUser')}:</strong> {t('users.deleteUserConfirmation', { name: 'John Doe' })}</p>
              <p><strong>{t('users.userInfo')}:</strong> {t('users.userInfoDescription')}</p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              {t('metadata.description')}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
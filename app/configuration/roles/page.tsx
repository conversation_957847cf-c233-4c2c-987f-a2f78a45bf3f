"use client";

import { RoleManagement } from "@/components/roles/role-management";
import React from "react";
import { toast } from "sonner";
import { useRoles } from "@/hooks/use-roles";
import { usePermissions } from "@/hooks/use-permissions";
import { useTranslations } from 'next-intl';

export default function Page() {
  const t = useTranslations();
  
  const {
    roles,
    loading: rolesLoading,
    error: rolesError,
    createRole,
    updateRole,
    deleteRole
  } = useRoles();

  const {
    permissions,
    loading: permissionsLoading,
    error: permissionsError
  } = usePermissions();

  const loading = rolesLoading || permissionsLoading;
  const error = rolesError || permissionsError;

  const handleCreateRole = async (roleData: { name: string; permissions: number[] }) => {
    try {
      await createRole(roleData);
      toast.success(t('roles.createRole'));
    } catch (error) {
      console.error('Error creating role:', error);
      toast.error(t('roles.createRoleError'));
    }
  };

  const handleUpdateRole = async (id: number, roleData: { name: string; permissions: number[] }) => {
    try {
      await updateRole(id, roleData);
      toast.success(t('roles.updateRole'));
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error(t('roles.updateRoleError'));
    }
  };

  const handleDeleteRole = async (id: number) => {
    try {
      await deleteRole(id);
      toast.success(t('roles.deleteRole'));
    } catch (error) {
      console.error('Error deleting role:', error);
      toast.error(t('roles.deleteRoleError'));
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">{t('common.loading')}</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-500">{t('common.error')}: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="">
        <RoleManagement
          roles={roles}
          permissions={permissions}
          onCreateRole={handleCreateRole}
          onUpdateRole={handleUpdateRole}
          onDeleteRole={handleDeleteRole}
        />
      </div>
    </div>
  );
}
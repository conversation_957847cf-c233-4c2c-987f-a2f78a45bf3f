import NextAuth from "next-auth"
import AzureADProvider from "next-auth/providers/azure-ad"
import type { NextAuthOptions } from "next-auth"

const authOptions: NextAuthOptions = {
  providers: [
    AzureADProvider({
      clientId: process.env.AZURE_AD_CLIENT_ID!,
      clientSecret: process.env.AZURE_AD_CLIENT_SECRET!,
      tenantId: process.env.AZURE_AD_TENANT_ID!,
    }),
  ],
  callbacks: {
    async jwt({ token, account, profile }) {
      console.log('🔑 JWT Callback - Account:', !!account, 'Profile:', !!profile)
      // Persist the OAuth access_token and or the user id to the token right after signin
      if (account) {
        console.log('💾 Storing tokens in JWT')
        token.accessToken = account.access_token
        token.idToken = account.id_token
      }
      return token
    },
    async session({ session, token }) {
      console.log('👤 Session Callback - User:', session.user?.email)
      // Send properties to the client, like an access_token and user id from a provider.
      session.accessToken = token.accessToken
      session.idToken = token.idToken
      session.djangoAccessToken = token.djangoAccessToken
      session.djangoRefreshToken = token.djangoRefreshToken

      // Verify the token with Django backend only if we don't have Django tokens yet
      if (!token.djangoAccessToken) {
        try {
          console.log('🔄 Verifying token with Django backend...')
          const response = await fetch(`${process.env.BACKEND_URL}/users/login/microsoft/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              token: token.accessToken
            }),
          })

          if (response.ok) {
            const userData = await response.json()
            console.log('✅ Django verification successful:', userData)
            session.user = { ...session.user, ...userData.usuario }
            session.djangoAccessToken = userData.access_token
            session.djangoRefreshToken = userData.refresh_token

            // Store Django tokens in the JWT token for future use
            token.djangoAccessToken = userData.access_token
            token.djangoRefreshToken = userData.refresh_token
          } else {
            console.warn('⚠️ Django verification failed:', response)
          }
        } catch (error) {
          console.error('❌ Error verifying with Django backend:', error)
          // Continue with session even if backend verification fails
        }
      }

      return session
    },
  },
  pages: {
    signIn: '/login',
  },
  session: {
    strategy: 'jwt',
  },
}

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST }

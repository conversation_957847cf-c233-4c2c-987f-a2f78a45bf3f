"use client";

import Link from "next/link";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Shield, Settings, Database, Cuboid } from "lucide-react";
import { useTranslations } from 'next-intl';

export default function BlocksPage() {
  const t = useTranslations();

  const blocksSections = [
    {
      title: t('blocks.blocksSection.title'),
      description: t('blocks.blocksSection.description'),
      icon: Cuboid,
      href: "/blocks/block",
      color: "text-blue-600"
    },
    {
      title: t('blocks.headerFooterSection.title'),
      description: t('blocks.headerFooterSection.description'),
      icon: Database,
      href: "/blocks/header-footer",
      color: "text-green-600"
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">{t('blocks.title')}</h1>
        <p className="text-muted-foreground">
          {t('blocks.description')}
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        {blocksSections.map((section) => {
          const IconComponent = section.icon;
          return (
            <Link key={section.title} href={section.href}>
              <Card className="h-full transition-colors hover:bg-muted/50">
                <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                  <div className="flex items-center space-x-2">
                    <IconComponent className={`h-6 w-6 ${section.color}`} />
                    <CardTitle className="text-lg">{section.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm">
                    {section.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>
    </div>
  );
}

"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { ArrowLeft, Plus, Eye, Save } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { HtmlEditor } from "@/components/ui/html-editor"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { useBrands } from "@/hooks/use-brands"
import { useCreateBlock } from "@/hooks/use-create-block"

interface Variable {
  name: string
  defaultValue: string
}

interface BlockFormData {
  name: string
  brand: string
  html_content: string
  description: string
  variables: Record<string, string>
  is_editable: boolean
  is_active: boolean
}

export default function CreateBlockPage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const { brands, loading: brandsLoading } = useBrands()
  const { createBlock, loading: createLoading, error: createError } = useCreateBlock()

  const [formData, setFormData] = useState<BlockFormData>({
    name: "",
    brand: "",
    html_content: "",
    description: "",
    variables: {},
    is_editable: true,
    is_active: true
  })

  const [variables, setVariables] = useState<Variable[]>([])
  const [previewHtml, setPreviewHtml] = useState("")

  const handleBack = () => {
    router.push('/blocks')
  }

  // Update preview HTML when content or variables change
  useEffect(() => {
    let html = formData.html_content

    // Replace variables in HTML with their default values
    Object.entries(formData.variables).forEach(([varName, defaultValue]) => {
      const regex = new RegExp(`\\{\\{\\s*${varName}\\s*\\}\\}`, 'g')
      html = html.replace(regex, defaultValue)
    })

    setPreviewHtml(html)
  }, [formData.html_content, formData.variables])

  // Extract variables from HTML content
  useEffect(() => {
    const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
    const foundVariables = new Set<string>()
    let match

    while ((match = variableRegex.exec(formData.html_content)) !== null) {
      foundVariables.add(match[1])
    }

    // Update variables list
    const newVariables: Variable[] = Array.from(foundVariables).map(varName => {
      const existing = variables.find(v => v.name === varName)
      return existing || { name: varName, defaultValue: '' }
    })

    setVariables(newVariables)

    // Update form variables
    const newFormVariables: Record<string, string> = {}
    newVariables.forEach(variable => {
      newFormVariables[variable.name] = variable.defaultValue
    })

    setFormData(prev => ({ ...prev, variables: newFormVariables }))
  }, [formData.html_content])

  const handleVariableChange = (varName: string, defaultValue: string) => {
    setVariables(prev =>
      prev.map(v => v.name === varName ? { ...v, defaultValue } : v)
    )

    setFormData(prev => ({
      ...prev,
      variables: { ...prev.variables, [varName]: defaultValue }
    }))
  }

  const loadExample = () => {
    const exampleHtml = `
    <div style="background-color: #f8f9fa; padding: 30px; max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h1 style="color: #5c6bc0; font-size: 28px; margin-bottom: 20px; text-align: center;">
            Welcome to Our Newsletter
        </h1>

        <p style="color: #555; line-height: 1.6; margin-bottom: 25px; font-size: 16px;">
            Check out our featured product of the month with special discounts!
        </p>

        <div style="text-align: center; margin-bottom: 25px; display: flex; justify-content: center; align-items: center;">
            <img src="https://media1.tenor.com/m/dsz4jV3F5vcAAAAd/sad-abitoads.gif" alt="Featured Product" style="max-width: 100%; height: auto; border-radius: 6px; border: 1px solid #e0e0e0; display: block; margin: 0 auto;" />
        </div>

        <div style="text-align: center; margin-bottom: 30px;">
            <a href="#product-link" style="background-color: #4caf50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block; margin-right: 15px;">
                Shop Now!
            </a>

            <button style="background-color: #ff9800; color: white; padding: 12px 24px; border: none; border-radius: 4px; font-weight: bold; cursor: pointer;">
                Learn More
            </button>
        </div>

        <div style="border-top: 1px solid #e0e0e0; padding-top: 20px; font-size: 14px; color: #777; text-align: center;">
            © 2024 Your Company. All rights reserved.
        </div>
    </div>
    `

    setFormData(prev => ({
      ...prev,
      name: "Example Block",
      description: "A sample block with variables",
      html_content: exampleHtml
    }))
  }

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      toast.error('Block name is required')
      return
    }

    if (!formData.brand) {
      toast.error('Please select a brand')
      return
    }

    if (!formData.html_content.trim()) {
      toast.error('HTML content is required')
      return
    }

    try {
      await createBlock(formData)
      toast.success('Block created successfully!')
      router.push('/blocks/block')
    } catch (error) {
      // Error is already handled in the hook and shown via toast
      console.error('Error creating block:', error)
    }
  }


  // Show loading while session is being fetched
  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">Loading...</div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create Block</h1>
          <p className="text-muted-foreground">
            Create a new content block for your newsletters with live preview
          </p>
        </div>
        <Button onClick={handleSubmit} disabled={createLoading}>
          <Save className="mr-2 h-4 w-4" />
          {createLoading ? 'Creating...' : 'Create Block'}
        </Button>
      </div>

      {/* Error Display */}
      {createError && (
        <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          Error: {createError}
        </div>
      )}

      {/* Block Details - Single Row */}
      <Card>
        <CardHeader>
          <CardTitle>Block Details</CardTitle>
          <CardDescription>
            Configure your block settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Block Name</Label>
              <Input
                id="name"
                placeholder="Enter block name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Brand</Label>
              <Select value={formData.brand} onValueChange={(value) => setFormData(prev => ({ ...prev, brand: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a brand" />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                placeholder="Enter block description (optional)"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Settings</Label>
              <div className="flex items-center gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.is_editable}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_editable: checked }))}
                  />
                  <Label className="text-sm">Editable</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                  />
                  <Label className="text-sm">Active</Label>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* HTML Editor and Preview - Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Panel - HTML Editor */}
        <div className="space-y-6">

          {/* HTML Editor */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>
                    HTML Content
                  </CardTitle>
                  <CardDescription>
                    Write your HTML content. Use {`{{ variableName }}`} for variables.
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" onClick={loadExample}>
                  <Plus className="mr-2 h-4 w-4" />
                  Load Example
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <HtmlEditor
                placeholder={`Enter your HTML content here...`}
                value={formData.html_content}
                onChange={(value) => setFormData(prev => ({ ...prev, html_content: value }))}
                rows={20}
              />
            </CardContent>
          </Card>
          {/* Variables */}
          {variables.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Variables</CardTitle>
                <CardDescription>
                  Set default values for variables found in your HTML
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {variables.map((variable) => (
                    <div key={variable.name} className="space-y-2">
                      <Label htmlFor={`var-${variable.name}`}>
                        {`{{ ${variable.name} }}`}
                      </Label>
                      <Input
                        id={`var-${variable.name}`}
                        placeholder={`Default value for ${variable.name}`}
                        value={variable.defaultValue}
                        onChange={(e) => handleVariableChange(variable.name, e.target.value)}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Panel - Preview and Variables */}
        <div
          className="space-y-6 min-w-[600px] sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto"
          style={{ zIndex: 10 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Live Preview
              </CardTitle>
              <CardDescription>
                See how your block will look with variable values
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-6 min-w-[600px] max-w-[640px] min-h-[500px] bg-white shadow-inner">
                {previewHtml ? (
                  <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Start typing HTML to see preview</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

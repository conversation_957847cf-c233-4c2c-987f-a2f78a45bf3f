"use client"

import React, { useState, useEffect } from "react"
import { Eye, X } from "lucide-react"
import { Block } from "@/types/block"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

interface ViewBlockModalProps {
  block: Block | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ViewBlockModal({ block, open, onOpenChange }: ViewBlockModalProps) {
  const [previewHtml, setPreviewHtml] = useState("")

  // Update preview HTML when block changes
  useEffect(() => {
    if (!block) return

    let html = block.html_content

    // Replace variables in HTML with their default values
    if (block.variables && typeof block.variables === 'object') {
      Object.entries(block.variables).forEach(([varName, defaultValue]) => {
        const regex = new RegExp(`\\{\\{\\s*${varName}\\s*\\}\\}`, 'g')
        html = html.replace(regex, String(defaultValue))
      })
    }

    setPreviewHtml(html)
  }, [block])

  if (!block) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            View Block: {block.name}
          </DialogTitle>
          <DialogDescription>
            Block details and preview
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Panel - Block Details */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Block Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Name</label>
                  <p className="text-sm">{block.name}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Brand</label>
                  <p className="text-sm">{block.brand_name}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Description</label>
                  <p className="text-sm">{block.description || 'No description provided'}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <div className="flex gap-2 mt-1">
                    <Badge variant="outline">
                      {block.is_editable ? 'Editable' : 'Read-only'}
                    </Badge>
                    <Badge variant={block.is_active ? 'default' : 'secondary'}>
                      {block.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>

                <Separator />

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created by</label>
                  <p className="text-sm">{block.created_by.first_name} {block.created_by.last_name}</p>
                  <p className="text-xs text-muted-foreground">{new Date(block.created_at).toLocaleString()}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last updated by</label>
                  <p className="text-sm">{block.updated_by.first_name} {block.updated_by.last_name}</p>
                  <p className="text-xs text-muted-foreground">{new Date(block.updated_at).toLocaleString()}</p>
                </div>
              </CardContent>
            </Card>

            {/* Variables */}
            {block.variables && Object.keys(block.variables).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Variables</CardTitle>
                  <CardDescription>Default values for template variables</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(block.variables).map(([key, value]) => (
                      <div key={key} className="flex justify-between items-center p-2 bg-muted rounded">
                        <span className="font-mono text-sm">{key}</span>
                        <span className="text-sm text-muted-foreground">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Panel - Preview */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Live Preview</CardTitle>
                <CardDescription>How the block looks with variable values</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-4 bg-white shadow-inner min-h-[400px]">
                  {previewHtml ? (
                    <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No content to preview</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* HTML Source */}
            <Card>
              <CardHeader>
                <CardTitle>HTML Source</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-muted p-4 rounded-lg overflow-x-auto max-h-60">
                  <code>{block.html_content}</code>
                </pre>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

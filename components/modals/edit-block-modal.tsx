"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { Block } from "@/types/block"
import { useBrands } from "@/hooks/use-brands"
import { useUpdateBlock } from "@/hooks/use-update-block"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { HtmlEditor } from "@/components/ui/html-editor"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"

interface Variable {
  name: string
  defaultValue: string
}

interface EditBlockModalProps {
  block: Block | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function EditBlockModal({ block, open, onOpenChange, onSuccess }: EditBlockModalProps) {
  const { brands, loading: brandsLoading } = useBrands()
  const { updateBlock, loading: updateLoading } = useUpdateBlock()

  const [formData, setFormData] = useState({
    name: "",
    brand: "",
    html_content: "",
    description: "",
    variables: {} as Record<string, string>,
    is_editable: true,
    is_active: true
  })

  const [variables, setVariables] = useState<Variable[]>([])
  const [previewHtml, setPreviewHtml] = useState("")

  // Initialize form data when block changes
  useEffect(() => {
    if (block) {
      setFormData({
        name: block.name,
        brand: block.brand,
        html_content: block.html_content,
        description: block.description || "",
        variables: block.variables || {},
        is_editable: block.is_editable ?? true,
        is_active: block.is_active ?? true
      })

      // Convert variables object to array for editing
      const varsArray = Object.entries(block.variables || {}).map(([name, defaultValue]) => ({
        name,
        defaultValue: String(defaultValue)
      }))
      setVariables(varsArray)
    }
  }, [block])

  // Update preview HTML when content or variables change
  useEffect(() => {
    let html = formData.html_content

    // Replace variables in HTML with their default values
    Object.entries(formData.variables).forEach(([varName, defaultValue]) => {
      const regex = new RegExp(`\\{\\{\\s*${varName}\\s*\\}\\}`, 'g')
      html = html.replace(regex, defaultValue)
    })

    setPreviewHtml(html)
  }, [formData.html_content, formData.variables])

  // Extract variables from HTML content (auto-detection like create modal)
  useEffect(() => {
    const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
    const foundVariables = new Set<string>()
    let match

    while ((match = variableRegex.exec(formData.html_content)) !== null) {
      foundVariables.add(match[1])
    }

    // Update variables list
    const newVariables: Variable[] = Array.from(foundVariables).map(varName => {
      const existing = variables.find(v => v.name === varName)
      return existing || { name: varName, defaultValue: '' }
    })

    setVariables(newVariables)

    // Update form variables
    const newFormVariables: Record<string, string> = {}
    newVariables.forEach(variable => {
      newFormVariables[variable.name] = variable.defaultValue
    })

    setFormData(prev => ({ ...prev, variables: newFormVariables }))
  }, [formData.html_content])

  const handleVariableChange = (varName: string, defaultValue: string) => {
    setVariables(prev =>
      prev.map(v => v.name === varName ? { ...v, defaultValue } : v)
    )

    setFormData(prev => ({
      ...prev,
      variables: { ...prev.variables, [varName]: defaultValue }
    }))
  }



  const handleSubmit = async () => {
    if (!block) return

    if (!formData.name.trim()) {
      toast.error('Block name is required')
      return
    }

    if (!formData.brand) {
      toast.error('Please select a brand')
      return
    }

    if (!formData.html_content.trim()) {
      toast.error('HTML content is required')
      return
    }

    try {
      await updateBlock({
        id: block.id,
        ...formData
      })
      onSuccess()
      onOpenChange(false)
    } catch (error) {
      console.error('Error updating block:', error)
    }
  }

  if (!block) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Pencil className="h-5 w-5" />
            Edit Block: {block.name}
          </DialogTitle>
          <DialogDescription>
            Update block information and content
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
          {/* Left Panel - Form */}
          <div className="space-y-6 lg:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle>Block Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter block name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="brand">Brand *</Label>
                  <Select
                    value={formData.brand}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, brand: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a brand" />
                    </SelectTrigger>
                    <SelectContent>
                      {brands.map((brand) => (
                        <SelectItem key={brand.id} value={brand.id}>
                          {brand.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter block description (optional)"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Settings</Label>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={formData.is_editable}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_editable: checked }))}
                      />
                      <Label className="text-sm">Editable</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={formData.is_active}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                      />
                      <Label className="text-sm">Active</Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>HTML Content</CardTitle>
                <CardDescription>
                  Write your HTML content. Use {`{{ variableName }}`} for variables.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <HtmlEditor
                  placeholder="Enter your HTML content here..."
                  value={formData.html_content}
                  onChange={(value) => setFormData(prev => ({ ...prev, html_content: value }))}
                  rows={20}
                />
              </CardContent>
            </Card>

            {/* Variables */}
            {variables.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Variables</CardTitle>
                  <CardDescription>
                    Set default values for variables found in your HTML
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {variables.map((variable) => (
                      <div key={variable.name} className="space-y-2">
                        <Label htmlFor={`var-${variable.name}`}>
                          {`{{ ${variable.name} }}`}
                        </Label>
                        <Input
                          id={`var-${variable.name}`}
                          placeholder={`Default value for ${variable.name}`}
                          value={variable.defaultValue}
                          onChange={(e) => handleVariableChange(variable.name, e.target.value)}
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Panel - Preview */}
          <div
            className="space-y-6 lg:col-span-4 min-w-[300px] sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto"
            style={{ zIndex: 10 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Live Preview
                </CardTitle>
                <CardDescription>
                  See how your block will look with variable values
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-6 min-w-[600px] max-w-[640px] min-h-[500px] bg-white shadow-inner">
                  {previewHtml ? (
                    <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No content to preview</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={updateLoading}>
            {updateLoading ? 'Updating...' : 'Update Block'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

"use client"

import React, { useEffect, use<PERSON>em<PERSON>, useState } from "react"
import { Pencil } from "lucide-react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useBrands } from "@/hooks/use-brands"
import { useBlocks } from "@/hooks/use-blocks"
import { useTemplateDetail } from "@/hooks/use-template-detail"
import { useUpdateTemplate } from "@/hooks/use-update-template"
import { toast } from "sonner"

interface EditTemplateModalProps {
  templateId: string | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function EditTemplateModal({ templateId, open, onOpenChange, onSuccess }: EditTemplateModalProps) {
  const { brands } = useBrands()
  const [brandFilter, setBrandFilter] = useState<string>("")
  const { blocks } = useBlocks({ page: 1, pageSize: 100, brand: brandFilter || undefined })
  const { template } = useTemplateDetail(templateId || undefined)
  const { updateTemplate, loading } = useUpdateTemplate()

  const [formData, setFormData] = useState({
    name: "",
    brand: "",
    description: "",
    is_active: true,
    template_blocks: [] as { block_id: string; order_position: number }[],
  })

  useEffect(() => {
    if (!template) return
    setFormData({
      name: template.name,
      brand: template.brand,
      description: template.description || "",
      is_active: template.is_active,
      template_blocks: template.template_blocks
        .sort((a, b) => a.order_position - b.order_position)
        .map((tb) => ({ block_id: tb.block.id, order_position: tb.order_position })),
    })
    setBrandFilter(template.brand)
  }, [template])

  // Clear selected blocks when brand changes
  useEffect(() => {
    setFormData(prev => ({ ...prev, template_blocks: [] }))
  }, [formData.brand])

  const availableBlocks = useMemo(() => blocks, [blocks])

  const handleAddBlock = (blockId: string) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: [
        ...prev.template_blocks,
        { block_id: blockId, order_position: prev.template_blocks.length + 1 },
      ],
    }))
  }

  const handleRemoveBlock = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: prev.template_blocks
        .filter((_, i) => i !== index)
        .map((tb, i) => ({ ...tb, order_position: i + 1 })),
    }))
  }

  const moveBlock = (index: number, direction: -1 | 1) => {
    setFormData((prev) => {
      const newBlocks = [...prev.template_blocks]
      const targetIndex = index + direction
      if (targetIndex < 0 || targetIndex >= newBlocks.length) return prev
      const [moved] = newBlocks.splice(index, 1)
      newBlocks.splice(targetIndex, 0, moved)
      return {
        ...prev,
        template_blocks: newBlocks.map((tb, i) => ({ ...tb, order_position: i + 1 })),
      }
    })
  }

  const handleSubmit = async () => {
    if (!templateId) return
    if (!formData.name.trim()) return toast.error('El nom és obligatori')
    if (!formData.brand) return toast.error('Selecciona una marca')

    try {
      await updateTemplate({ id: templateId, ...formData })
      onSuccess()
      onOpenChange(false)
    } catch (e) {
      console.error(e)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Pencil className="h-5 w-5" />
            Editar plantilla
          </DialogTitle>
          <DialogDescription>Actualitza la informació i els blocs de la plantilla</DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Informació bàsica</CardTitle>
                <CardDescription>Nom, marca i descripció</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Nom</Label>
                  <Input value={formData.name} onChange={(e) => setFormData((p) => ({ ...p, name: e.target.value }))} />
                </div>
                <div className="space-y-2">
                  <Label>Marca</Label>
                  <Select value={formData.brand} onValueChange={(v) => { setBrandFilter(v); setFormData((p) => ({ ...p, brand: v, template_blocks: [] })); }}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona una marca" />
                    </SelectTrigger>
                    <SelectContent>
                      {brands.map((b) => (
                        <SelectItem key={b.id} value={b.id}>{b.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Descripció</Label>
                  <Input value={formData.description} onChange={(e) => setFormData((p) => ({ ...p, description: e.target.value }))} />
                </div>
                <div className="flex items-center justify-between">
                  <Label>Activa</Label>
                  <Switch checked={formData.is_active} onCheckedChange={(v) => setFormData((p) => ({ ...p, is_active: v }))} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Blocs de la plantilla</CardTitle>
                <CardDescription>Afegir, treure i ordenar blocs</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Disable until brand selected */}
                {!formData.brand && (
                  <div className="p-4 text-sm text-muted-foreground border rounded">
                    Selecciona una marca per veure i afegir blocs.
                  </div>
                )}

                {formData.brand && (
                  <div className="overflow-hidden rounded-lg border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50%]">Nom del bloc</TableHead>
                          <TableHead>Marca</TableHead>
                          <TableHead className="text-right">Accions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {availableBlocks.length ? (
                          availableBlocks.map((b) => (
                            <TableRow key={b.id}>
                              <TableCell className="font-medium">{b.name}</TableCell>
                              <TableCell>{b.brand_name}</TableCell>
                              <TableCell className="text-right">
                                <Button size="sm" variant="outline" onClick={() => handleAddBlock(b.id)}>
                                  Afegir
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={3} className="text-center text-sm text-muted-foreground h-16">
                              No hi ha blocs disponibles per a la marca seleccionada.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}

                <ol className="space-y-2 list-decimal pl-6">
                  {formData.template_blocks.map((tb, idx) => {
                    const block = availableBlocks.find((b) => b.id === tb.block_id)
                    return (
                      <li key={`${tb.block_id}-${idx}`} className="flex items-center gap-2">
                        <span className="flex-1">{block?.name || tb.block_id}</span>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline" onClick={() => moveBlock(idx, -1)}>Amunt</Button>
                          <Button size="sm" variant="outline" onClick={() => moveBlock(idx, 1)}>Avall</Button>
                          <Button size="sm" variant="destructive" onClick={() => handleRemoveBlock(idx)}>Treure</Button>
                        </div>
                      </li>
                    )
                  })}
                </ol>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            {/* Live preview */}
            <Card>
              <CardHeader>
                <CardTitle>Previsualització</CardTitle>
                <CardDescription>Mostra del resultat concatenant els blocs</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-4 bg-white max-h-[60vh] overflow-auto min-w-[320px]">
                  {formData.template_blocks.length ? (
                    <div dangerouslySetInnerHTML={{ __html: formData.template_blocks
                      .map(tb => (blocks.find(b => b.id === tb.block_id)?.html_content || ""))
                      .join("\n") }} />
                  ) : (
                    <div className="text-sm text-muted-foreground">Afegeix blocs per veure la previsualització.</div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Accions</CardTitle>
              </CardHeader>
              <CardContent className="flex gap-2">
                <Button onClick={handleSubmit} disabled={loading}>
                  {loading ? 'Desant...' : 'Desar canvis'}
                </Button>
                <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel·lar</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}


"use client"

import React from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Header<PERSON>ooter } from "@/types/block"

interface ViewHeaderFooterModalProps {
  headerFooter: HeaderFooter | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ViewHeaderFooterModal({
  headerFooter,
  open,
  onOpenChange,
}: ViewHeaderFooterModalProps) {
  if (!headerFooter) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {headerFooter.name}
            <Badge variant={headerFooter.element_type === "header" ? "default" : "secondary"}>
              {headerFooter.element_type_display}
            </Badge>
            <Badge variant={headerFooter.is_active ? "default" : "destructive"}>
              {headerFooter.is_active ? "Active" : "Inactive"}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            View header/footer details and HTML content
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Name</Label>
              <p className="text-sm">{headerFooter.name}</p>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Brand</Label>
              <p className="text-sm">{headerFooter.brand_name}</p>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Type</Label>
              <p className="text-sm">{headerFooter.element_type_display}</p>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Language</Label>
              <p className="text-sm">{headerFooter.language_display}</p>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Status</Label>
              <p className="text-sm">
                <Badge variant={headerFooter.is_active ? "default" : "destructive"}>
                  {headerFooter.is_active ? "Active" : "Inactive"}
                </Badge>
              </p>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">ID</Label>
              <p className="text-sm font-mono text-xs break-all">{headerFooter.id}</p>
            </div>
          </div>

          <Separator />

          {/* Timestamps */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Created</Label>
              <p className="text-sm">{new Date(headerFooter.created_at).toLocaleString()}</p>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Last Updated</Label>
              <p className="text-sm">{new Date(headerFooter.updated_at).toLocaleString()}</p>
            </div>
          </div>

          <Separator />

          {/* HTML Content */}
          <div className="space-y-4">
            <Label className="text-sm font-medium text-muted-foreground">HTML Content</Label>
            
            {/* Raw HTML */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Raw HTML</Label>
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-xs overflow-x-auto whitespace-pre-wrap break-words">
                  {headerFooter.html_content}
                </pre>
              </div>
            </div>

            {/* Rendered Preview */}
            <div className="space-y-2">
              <Label className="text-xs font-medium text-muted-foreground">Preview</Label>
              <div className="border rounded-md p-4 bg-white">
                <div 
                  dangerouslySetInnerHTML={{ __html: headerFooter.html_content }}
                  className="prose prose-sm max-w-none"
                />
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

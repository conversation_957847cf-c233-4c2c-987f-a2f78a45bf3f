'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useLanguageContext } from "@/components/providers/language-provider";
import { Languages } from "lucide-react";
import { SUPPORTED_LOCALES, type SupportedLocale } from "@/lib/constants";

export function LanguageSwitcher() {
  const { locale, setLocale } = useLanguageContext();

  const languages = [
    { code: 'es' as SupportedLocale, name: 'Español' },
    { code: 'ca' as SupportedLocale, name: 'Català' }
  ];

  return (
    <div className="flex items-center space-x-2">
      <Languages className="h-4 w-4" />
      <Select value={locale} onValueChange={(value: SupportedLocale) => setLocale(value)}>
        <SelectTrigger className="w-32">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {languages.map((lang) => (
            <SelectItem key={lang.code} value={lang.code}>
              {lang.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
"use client"

import * as React from "react"
import { ColumnDef, ColumnFiltersState, SortingState, VisibilityState, flexRender, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from "@tanstack/react-table"
import { Eye, Pencil, MoreHorizontal, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"

import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"

import { TemplateListItem } from "@/types/template"
import { useDeleteTemplate } from "@/hooks/use-delete-template"
import { ViewTemplateModal } from "@/components/modals/view-template-modal"
import { EditTemplateModal } from "@/components/modals/edit-template-modal"
import { useTranslations } from "next-intl"

interface TemplatesTableProps {
  data: TemplateListItem[]
  loading: boolean
  onRefresh: () => void
  pagination?: {
    page: number
    pageSize: number
    totalCount: number
    hasNext: boolean
    hasPrevious: boolean
    onPageChange: (page: number) => void
    onPageSizeChange: (size: number) => void
  }
}

export function TemplatesTable({ data, loading, onRefresh, pagination }: TemplatesTableProps) {
  const t = useTranslations()

  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})

  const [viewModalOpen, setViewModalOpen] = React.useState(false)
  const [editModalOpen, setEditModalOpen] = React.useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false)
  const [selectedTemplate, setSelectedTemplate] = React.useState<TemplateListItem | null>(null)

  const { deleteTemplate, loading: deleteLoading } = useDeleteTemplate()

  const handleView = (tpl: TemplateListItem) => {
    setSelectedTemplate(tpl)
    setViewModalOpen(true)
  }

  const handleEdit = (tpl: TemplateListItem) => {
    setSelectedTemplate(tpl)
    setEditModalOpen(true)
  }

  const handleDelete = (tpl: TemplateListItem) => {
    setSelectedTemplate(tpl)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!selectedTemplate) return
    try {
      await deleteTemplate(selectedTemplate.id)
      setDeleteDialogOpen(false)
      setSelectedTemplate(null)
      onRefresh()
    } catch (e) {
      console.error(e)
    }
  }

  const handleEditSuccess = () => {
    onRefresh()
  }

  const columns: ColumnDef<TemplateListItem>[] = [
    {
      accessorKey: "name",
      header: t('common.name'),
      cell: ({ row }) => <div className="font-medium pl-2">{row.getValue("name")}</div>,
    },
    { accessorKey: "brand_name", header: t('common.brand') },
    {
      accessorKey: "is_active",
      header: t('common.status'),
      cell: ({ row }) =>
        row.getValue<boolean>("is_active") ? (
          <Badge className="bg-green-600 hover:bg-green-600">{t('common.active')}</Badge>
        ) : (
          <Badge variant="outline" className="border-gray-300 text-gray-600">{t('common.inactive')}</Badge>
        ),
    },
    { accessorKey: "total_blocks", header: t('common.blocks') },
    {
      id: "actions",
      header: t('common.actions'),
      cell: ({ row }) => {
        const tpl = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">{t('common.openMenu')}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t('common.actions')}</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(tpl.id)}>
                {t('common.copyTemplateId')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleView(tpl)}>
                <Eye className="mr-2 h-4 w-4" />
                {t('common.view')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEdit(tpl)}>
                <Pencil className="mr-2 h-4 w-4" />
                {t('common.edit')}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(tpl)}>
                {t('common.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    ...(pagination ? {} : { getPaginationRowModel: getPaginationRowModel() }),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    ...(pagination ? {
      manualPagination: true,
      pageCount: Math.ceil(pagination.totalCount / pagination.pageSize),
    } : {}),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      ...(pagination ? {
        pagination: { pageIndex: pagination.page - 1, pageSize: pagination.pageSize },
      } : {}),
    },
  })

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header, i) => (
                  <TableHead key={header.id} className={i === 0 ? "pl-4" : ""}>
                    {header.isPlaceholder ? null : header.column.columnDef.header as React.ReactNode}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  {t('common.loading')}
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  {t('common.noResults')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {pagination && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            {data.length} de {pagination.totalCount} {t('common.results')}
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" className="hidden h-8 w-8 p-0 lg:flex" onClick={() => pagination.onPageChange(1)} disabled={!pagination.hasPrevious}>
              <span className="sr-only">{t('table.firstPage')}</span>
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" className="h-8 w-8 p-0" onClick={() => pagination.onPageChange(pagination.page - 1)} disabled={!pagination.hasPrevious}>
              <span className="sr-only">{t('table.previous')}</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" className="h-8 w-8 p-0" onClick={() => pagination.onPageChange(pagination.page + 1)} disabled={!pagination.hasNext}>
              <span className="sr-only">{t('table.next')}</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button variant="outline" className="hidden h-8 w-8 p-0 lg:flex" onClick={() => pagination.onPageChange(Math.ceil(pagination.totalCount / pagination.pageSize))} disabled={!pagination.hasNext}>
              <span className="sr-only">{t('table.lastPage')}</span>
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* View Modal */}
      <ViewTemplateModal templateId={selectedTemplate?.id || null} open={viewModalOpen} onOpenChange={setViewModalOpen} />

      {/* Edit Modal */}
      <EditTemplateModal templateId={selectedTemplate?.id || null} open={editModalOpen} onOpenChange={setEditModalOpen} onSuccess={handleEditSuccess} />

      {/* Delete Confirmation */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('modal.deleteConfirmation')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('modal.template.deleteDescription', { templateName: selectedTemplate?.name.toString() || '' })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} disabled={deleteLoading} className="bg-red-600 hover:bg-red-700">
              {deleteLoading ? t('common.loading') : t('common.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}


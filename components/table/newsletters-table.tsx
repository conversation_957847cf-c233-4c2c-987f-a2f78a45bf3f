"use client"

import * as React from "react"
import { z } from "zod"
import { useTranslations } from 'next-intl'
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ColumnsIcon, Copy, Eye, MoreHorizontal, Pen<PERSON>l, Star, Trash } from "lucide-react"
import { Newsletter } from "@/types/newsletter"

export const newsletterSchema = z.object({
  id: z.string(),
  name: z.string(),
  brand_name: z.string(),
  language_display: z.string(),
  subject: z.string(),
  status_display: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
})

interface NewslettersTableProps {
  data: Newsletter[]
  loading?: boolean
}

export function NewslettersTable({ data, loading = false }: NewslettersTableProps) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>([])
  const t = useTranslations()

  const columns: ColumnDef<Newsletter>[] = [
    {
      accessorKey: "name",
      header: t('newsletters.newsletterName'),
      cell: ({ row }) => (
        <div className="font-medium pl-2">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "brand_name", 
      header: t('newsletters.brand'),
      cell: ({ row }) => (
        <Badge variant="outline" className="px-2 py-1">
          {row.getValue("brand_name")}
        </Badge>
      ),
    },
    {
      accessorKey: "language_display",
      header: t('newsletters.language'),
      cell: ({ row }) => (
        <div className="text-sm">{row.getValue("language_display")}</div>
      ),
    },
    {
      accessorKey: "subject",
      header: t('newsletters.subject'),
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate" title={row.getValue("subject")}>
          {row.getValue("subject")}
        </div>
      ),
    },
    {
      accessorKey: "status_display",
      header: t('newsletters.status'),
      cell: ({ row }) => {
        const status = row.getValue("status_display") as string
        return (
          <Badge
            variant={status === "Publicado" ? "default" : "secondary"}
            className="px-2 py-1"
          >
            {status}
          </Badge>
        )
      },
    },
    {
      accessorKey: "created_at",
      header: t('newsletters.created'),
      cell: ({ row }) => {
        const date = new Date(row.getValue("created_at"))
        return <div className="text-sm">{date.toLocaleDateString()}</div>
      },
    },
  ]
  const actionsColumn: ColumnDef<Newsletter>[] = [
    ...columns,
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const newsletter = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant={"ghost"} className="h-8 w-8 p-0">
                <span className="sr-only">{t('common.openMenu')}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t('common.actions')}</DropdownMenuLabel>
              <DropdownMenuItem>
                <Eye className="mr-2 h-4 w-4" />
                {t('common.view')}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Pencil className="mr-2 h-4 w-4" />
                {t('common.edit')}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" />
                {t('common.copy')}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Star className="mr-2 h-4 w-4" />
                {t('common.favorite')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <Trash className="mr-2 h-4 w-4" />
                {t('common.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    }
  ]


  const table = useReactTable({
    data,
    columns: actionsColumn,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    getRowId: (row) => row.id,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-muted-foreground">{t('newsletters.loadingNewsletters')}</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            {t('newsletters.rowsSelected', {
              selected: table.getFilteredSelectedRowModel().rows.length,
              total: table.getFilteredRowModel().rows.length
            })}
          </span>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <ColumnsIcon className="mr-2 h-4 w-4" />
              {t('common.columns')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {(() => {
              const columnNames: Record<string, string> = {
                name: t('newsletters.newsletterName'),
                brand_name: t('newsletters.brand'),
                language_display: t('newsletters.language'),
                subject: t('newsletters.subject'),
                status_display: t('newsletters.status'),
                created_at: t('newsletters.created'),
                updated_at: t('newsletters.updated'),
              };
              return table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  const displayName = columnNames[column.id] || column.id;
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {displayName}
                    </DropdownMenuCheckboxItem>
                  );
                });
            })()}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header, index) => {
                  return (
                    <TableHead key={header.id} className={index === 0 ? "pl-4" : ""}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={actionsColumn.length}
                  className="h-24 text-center"
                >
                  {t('newsletters.noNewslettersFound')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

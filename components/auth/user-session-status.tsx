"use client"

import { useMe } from '@/hooks/use-me'
import useUserStore from '@/store/use-user-store'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { RefreshCw, LogOut, User, Clock } from 'lucide-react'

/**
 * Component to display user session status and token information
 * Useful for debugging and monitoring the auto-refresh functionality
 */
export function UserSessionStatus() {
  const { user, loading, error, isRefreshing, refetch } = useMe()
  const { logout } = useUserStore()

  if (!user?.session) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Session
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            Loading user session...
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full max-w-md border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <User className="h-5 w-5" />
            Session Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 text-sm mb-4">{error}</p>
          <Button onClick={refetch} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!user) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            No Session
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">No user session found</p>
        </CardContent>
      </Card>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getTimeUntilExpiry = (expiresAt: string) => {
    const expiry = new Date(expiresAt).getTime()
    const now = Date.now()
    const diff = expiry - now
    
    if (diff <= 0) return 'Expired'
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  const isTokenExpired = (expiresAt: string) => {
    return new Date(expiresAt).getTime() <= Date.now()
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          User Session
          {isRefreshing && (
            <Badge variant="secondary" className="ml-auto">
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
              Refreshing
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          {user.user.first_name} {user.user.last_name} ({user.user.email})
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Access Token Status */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Access Token</span>
            <Badge 
              variant={isTokenExpired(user.session.access_token_expires_at) ? "destructive" : "default"}
            >
              {isTokenExpired(user.session.access_token_expires_at) ? "Expired" : "Valid"}
            </Badge>
          </div>
          <div className="text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              Expires: {formatDate(user.session.access_token_expires_at)}
            </div>
            <div>
              Time left: {getTimeUntilExpiry(user.session.access_token_expires_at)}
            </div>
          </div>
        </div>

        {/* Refresh Token Status */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Refresh Token</span>
            <Badge 
              variant={isTokenExpired(user.session.refresh_token_expires_at) ? "destructive" : "secondary"}
            >
              {isTokenExpired(user.session.refresh_token_expires_at) ? "Expired" : "Valid"}
            </Badge>
          </div>
          <div className="text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              Expires: {formatDate(user.session.refresh_token_expires_at)}
            </div>
            <div>
              Time left: {getTimeUntilExpiry(user.session.refresh_token_expires_at)}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button onClick={refetch} variant="outline" size="sm" disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={logout} variant="destructive" size="sm">
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

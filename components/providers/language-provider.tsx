'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { DEFAULT_LANGUAGE, SUPPORTED_LOCALES, type SupportedLocale } from '@/lib/constants';

interface LanguageContextType {
  locale: SupportedLocale;
  setLocale: (locale: SupportedLocale) => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocaleState] = useState<SupportedLocale>(DEFAULT_LANGUAGE);

  const setLocale = (newLocale: SupportedLocale) => {
    setLocaleState(newLocale);
    // Store in cookie
    document.cookie = `locale=${newLocale}; path=/; max-age=31536000`; // 1 year
    // Reload to apply new translations
    window.location.reload();
  };

  useEffect(() => {
    // Get locale from cookie on mount
    const cookieValue = document.cookie
      .split('; ')
      .find(row => row.startsWith('locale='))
      ?.split('=')[1];
    
    if (SUPPORTED_LOCALES.includes(cookieValue as any)) {
      setLocaleState(cookieValue as SupportedLocale);
    }
  }, []);

  return (
    <LanguageContext.Provider value={{ locale, setLocale }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguageContext() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguageContext must be used within a LanguageProvider');
  }
  return context;
}
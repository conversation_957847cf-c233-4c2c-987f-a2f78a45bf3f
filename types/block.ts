export interface User {
  id: string
  first_name: string
  last_name: string
  email: string
  updated_at_platform: string
  created_at_platform: string
}

export interface Block {
  id: string
  name: string
  brand: string
  brand_name: string
  description: string
  html_content: string
  variables: Record<string, any>
  created_by: User
  updated_by: User
  created_at: string
  updated_at: string
  is_editable?: boolean
  is_active?: boolean
}

export interface BlockResponse {
  count: number
  next: string | null
  previous: string | null
  results: Block[]
}

export interface HeaderFooter {
  id: string
  name: string
  brand: string
  brand_name: string
  element_type: string
  element_type_display: string
  language: string
  language_display: string
  html_content: string
  variables: Record<string, string>
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface HeaderFooterResponse {
  count: number
  next: string | null
  previous: string | null
  results: HeaderFooter[]
}

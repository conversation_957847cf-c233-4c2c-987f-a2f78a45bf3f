import {getRequestConfig} from 'next-intl/server';
import {cookies} from 'next/headers';
import { DEFAULT_LANGUAGE, SUPPORTED_LOCALES } from '@/lib/constants';

export default getRequestConfig(async () => {
  // Get locale from cookies or default to Catalan
  const cookieStore = await cookies();
  const locale = cookieStore.get('locale')?.value || DEFAULT_LANGUAGE;

  // Validate that the incoming `locale` parameter is valid
  const validLocale = SUPPORTED_LOCALES.includes(locale as any) ? locale : DEFAULT_LANGUAGE;

  return {
    locale: validLocale,
    messages: (await import(`../messages/${validLocale}.json`)).default
  };
});